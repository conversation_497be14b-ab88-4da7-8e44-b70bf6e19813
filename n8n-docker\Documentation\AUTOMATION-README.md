# 🤖 n8n + nGrok Automation Scripts Guide

Complete automation for starting n8n with nGrok tunneling and automatic webhook URL management.

## 🚀 Quick Start Methods

### 🖱️ Method 1: Double-Click (Easiest)
1. **Start Everything**: Double-click `start-n8n.bat`
2. **Stop Everything**: Double-click `stop-n8n.bat`

### 💻 Method 2: Command Line
```powershell
# Start everything (Docker + nGrok + URL updates)
powershell -ExecutionPolicy Bypass -File "Start-N8N-NgRok.ps1"

# Stop everything (nGrok + optionally Docker)
powershell -ExecutionPolicy Bypass -File "Stop-N8N-NgRok.ps1"
```

### 📁 Method 3: From File Explorer
1. Navigate to `n8n-docker` folder
2. Right-click `Start-N8N-NgRok.ps1` → "Run with PowerShell"
3. Or right-click `Stop-N8N-NgRok.ps1` → "Run with PowerShell"

## ✨ What Gets Automated

### 🚀 Starting Process (`Start-N8N-NgRok.ps1`):
1. ✅ **Pre-flight Checks** - Verifies Docker Desktop is running
2. ✅ **Environment Setup** - Creates .env and config.ps1 if missing
3. ✅ **Container Management** - Launches n8n and PostgreSQL via docker-compose
4. ✅ **Health Monitoring** - Waits for n8n to be fully ready (monitors logs)
5. ✅ **Tunnel Creation** - Starts nGrok tunnel in background process
6. ✅ **URL Discovery** - Extracts public HTTPS URL from nGrok API
7. ✅ **Configuration Update** - Automatically updates .env WEBHOOK_URL
8. ✅ **Service Restart** - Restarts n8n to apply new webhook configuration
9. ✅ **Status Display** - Shows all access URLs and connection info
10. ✅ **Error Handling** - Graceful handling of failures with clear messages

### 🛑 Stopping Process (`Stop-N8N-NgRok.ps1`):
1. ✅ **Process Termination** - Safely terminates all nGrok processes
2. ✅ **Container Management** - Optionally stops Docker containers
3. ✅ **Cleanup** - Removes temporary files and processes
4. ✅ **Status Confirmation** - Shows what was stopped and current state

## 🎛️ Advanced Script Parameters

### 🚀 Start Script Options (`Start-N8N-NgRok.ps1`)
```powershell
# Skip Docker startup (if containers already running)
Start-N8N-NgRok.ps1 -SkipDocker

# Skip nGrok startup (if tunnel already running)
Start-N8N-NgRok.ps1 -SkipNgrok

# Only update URLs without starting services
Start-N8N-NgRok.ps1 -UpdateUrlsOnly

# Use development Docker setup (SQLite instead of PostgreSQL)
Start-N8N-NgRok.ps1 -DevMode

# Enable verbose output for debugging
Start-N8N-NgRok.ps1 -Verbose

# Force restart even if services are running
Start-N8N-NgRok.ps1 -Force

# Specify custom nGrok config file
Start-N8N-NgRok.ps1 -NgrokConfig "custom-ngrok.yml"
```

### 🛑 Stop Script Options (`Stop-N8N-NgRok.ps1`)
```powershell
# Keep Docker containers running (only stop nGrok)
Stop-N8N-NgRok.ps1 -KeepDocker

# Force stop all processes (even if they seem stuck)
Stop-N8N-NgRok.ps1 -Force

# Enable verbose output for debugging
Stop-N8N-NgRok.ps1 -Verbose

# Only stop nGrok, leave everything else running
Stop-N8N-NgRok.ps1 -NgrokOnly
```

### 🔧 Parameter Combinations
```powershell
# Quick restart of just nGrok tunnel
Stop-N8N-NgRok.ps1 -NgrokOnly
Start-N8N-NgRok.ps1 -SkipDocker

# Full verbose restart
Stop-N8N-NgRok.ps1 -Verbose
Start-N8N-NgRok.ps1 -Verbose -Force

# Development mode with verbose output
Start-N8N-NgRok.ps1 -DevMode -Verbose
```

## 🔧 How It Works

### nGrok URL Detection
- Uses nGrok's REST API at `http://127.0.0.1:4040/api/tunnels`
- Automatically finds the HTTPS tunnel endpoint
- No manual copy/paste needed!

### Environment File Updates
- Reads current `.env` file
- Updates `WEBHOOK_URL=` line with new nGrok URL
- Preserves all other environment variables

### Smart Detection
- Checks if Docker containers are already running
- Detects existing nGrok tunnels
- Skips unnecessary steps to save time

## 🚨 Comprehensive Troubleshooting

### 🔒 PowerShell Execution Policy Issues
```powershell
# Error: "execution of scripts is disabled on this system"
# Solution 1: Run with bypass (temporary)
powershell -ExecutionPolicy Bypass -File "Start-N8N-NgRok.ps1"

# Solution 2: Change policy for current user (permanent)
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# Solution 3: Run as Administrator and set system-wide
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope LocalMachine
```

### 🐳 Docker Issues
```
❌ Docker is not running. Please start Docker Desktop first.
```
**Solutions:**
1. Start Docker Desktop and wait for "Docker Desktop is running" status
2. Check Docker is responding: `docker info`
3. Restart Docker Desktop if stuck
4. Check available disk space (Docker needs ~10GB)

```
❌ Container failed to start
```
**Solutions:**
1. Check logs: `docker-compose logs n8n`
2. Check port conflicts: `netstat -an | findstr 5678`
3. Restart Docker: `docker-compose down && docker-compose up -d`
4. Check .env file syntax

### 🌐 nGrok Issues
```
⚠️ nGrok tunnel timeout - check manually
```
**Solutions:**
1. Check nGrok status: http://127.0.0.1:4040
2. Verify nGrok authentication: `ngrok config check`
3. Check nGrok account limits (free plan = 1 tunnel)
4. Restart nGrok: Kill process and restart script

```
❌ nGrok authentication failed
```
**Solutions:**
1. Check auth token: `ngrok config check`
2. Re-authenticate: `ngrok config add-authtoken YOUR_TOKEN`
3. Verify account is active at https://dashboard.ngrok.com/

### 🔧 n8n Startup Issues
```
⚠️ n8n startup timeout - check logs manually
```
**Solutions:**
1. Check container logs: `docker logs n8n-dev --tail 50`
2. Check database connection: `docker logs postgres --tail 20`
3. Verify .env file configuration
4. Check available memory (need 4GB+ for Docker)

```
❌ n8n web interface not accessible
```
**Solutions:**
1. Verify container is running: `docker-compose ps`
2. Check port mapping: `docker port n8n-dev`
3. Try different browser or incognito mode
4. Check Windows Firewall settings

## 📁 File Structure

```
n8n-docker/
├── start-n8n.bat              # Windows batch file (double-click)
├── stop-n8n.bat               # Windows batch file (double-click)
├── Start-N8N-NgRok.ps1        # Main PowerShell start script
├── Stop-N8N-NgRok.ps1         # Main PowerShell stop script
├── docker-compose.yml         # Docker configuration
├── .env                       # Environment variables (auto-updated)
└── RunSystem.md               # Complete manual instructions
```

## 🎯 Automation Benefits

### 📋 Before Automation (Manual Process):
1. ✋ Start Docker Desktop manually
2. 💻 Open terminal, navigate to n8n-docker folder
3. ⌨️ Type `docker-compose up -d`
4. ⏳ Wait and monitor for n8n startup messages
5. 💻 Open second terminal for nGrok
6. ⌨️ Type nGrok command with correct parameters
7. 👀 Watch terminal output for public URL
8. 📋 Copy URL from terminal (prone to errors)
9. 📝 Edit .env file manually with new URL
10. ⌨️ Type `docker-compose restart n8n`
11. 🔍 Check everything works correctly
12. 🔄 Repeat steps 6-11 every time nGrok restarts

**⏱️ Time Required**: 5-10 minutes per startup
**🚨 Error Prone**: Manual copy/paste, typos, missed steps
**😤 Frustration**: Repetitive, boring, easy to forget steps

### ✨ After Automation (One-Click Process):
1. **🖱️ Double-click `start-n8n.bat`**
2. **☕ Get coffee while automation runs**
3. **✅ Everything ready and tested!**

**⏱️ Time Required**: 30 seconds of your time
**✅ Error Free**: No manual steps, automated validation
**😊 Satisfaction**: Reliable, fast, consistent results

### 📊 Quantified Benefits:
- **Time Saved**: 5-10 minutes per startup → 30 seconds
- **Error Reduction**: ~90% fewer manual errors
- **Consistency**: Same process every time
- **Reliability**: Automated error detection and recovery
- **Convenience**: Works from anywhere (desktop shortcuts)

### 🚀 Additional Automation Features:
- **Smart Detection**: Skips steps if services already running
- **Error Recovery**: Handles common failure scenarios
- **Status Reporting**: Clear feedback on what's happening
- **URL Management**: Automatic webhook URL updates
- **Health Checks**: Verifies everything works before finishing

---

💡 **Pro Tips**:
- Create desktop shortcuts to `.bat` files for instant access
- Use `-Verbose` parameter when troubleshooting
- Keep nGrok running between development sessions to avoid URL changes
